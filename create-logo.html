<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>إنشاء شعار تجريبي</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            padding: 20px;
            background: #f5f5f5;
            text-align: center;
        }
        .logo-creator {
            max-width: 600px;
            margin: 0 auto;
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 4px 20px rgba(0,0,0,0.1);
        }
        .logo-preview {
            width: 200px;
            height: 200px;
            margin: 20px auto;
            background: linear-gradient(135deg, #2563eb, #3b82f6);
            border-radius: 20px;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 4rem;
            box-shadow: 0 8px 25px rgba(37, 99, 235, 0.3);
        }
        .download-btn {
            background: #2563eb;
            color: white;
            border: none;
            padding: 15px 30px;
            border-radius: 8px;
            font-size: 16px;
            cursor: pointer;
            margin: 10px;
        }
        .download-btn:hover {
            background: #1e40af;
        }
        .info {
            background: #e0f2fe;
            padding: 15px;
            border-radius: 8px;
            margin: 20px 0;
            border-left: 4px solid #2563eb;
        }
    </style>
</head>
<body>
    <div class="logo-creator">
        <h1>🎨 إنشاء شعار تجريبي للنظام</h1>
        
        <div class="info">
            <p><strong>ملاحظة:</strong> هذا شعار تجريبي لاختبار النظام. يمكنك استبداله بشعارك الخاص لاحقاً.</p>
        </div>
        
        <div class="logo-preview" id="logoPreview">
            <i class="fas fa-shield-alt"></i>
        </div>
        
        <button class="download-btn" onclick="downloadLogo()">
            📥 تحميل الشعار (logo.jpg)
        </button>
        
        <button class="download-btn" onclick="createDifferentLogo()">
            🔄 تغيير التصميم
        </button>
        
        <div class="info">
            <h3>كيفية استخدام الشعار:</h3>
            <ol style="text-align: right;">
                <li>اضغط على "تحميل الشعار" لحفظ الملف</li>
                <li>ضع الملف في نفس مجلد النظام</li>
                <li>تأكد من أن اسم الملف هو "logo.jpg"</li>
                <li>أعد تحميل صفحات النظام لرؤية الشعار</li>
            </ol>
        </div>
    </div>

    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    
    <script>
        const designs = [
            { bg: 'linear-gradient(135deg, #2563eb, #3b82f6)', icon: 'fas fa-shield-alt' },
            { bg: 'linear-gradient(135deg, #059669, #10b981)', icon: 'fas fa-lock' },
            { bg: 'linear-gradient(135deg, #dc2626, #ef4444)', icon: 'fas fa-user-shield' },
            { bg: 'linear-gradient(135deg, #7c3aed, #a855f7)', icon: 'fas fa-key' },
            { bg: 'linear-gradient(135deg, #ea580c, #f97316)', icon: 'fas fa-fingerprint' },
            { bg: 'linear-gradient(135deg, #0891b2, #06b6d4)', icon: 'fas fa-eye' }
        ];
        
        let currentDesign = 0;
        
        function createDifferentLogo() {
            currentDesign = (currentDesign + 1) % designs.length;
            const preview = document.getElementById('logoPreview');
            const design = designs[currentDesign];
            
            preview.style.background = design.bg;
            preview.innerHTML = `<i class="${design.icon}"></i>`;
        }
        
        function downloadLogo() {
            const canvas = document.createElement('canvas');
            const ctx = canvas.getContext('2d');
            canvas.width = 200;
            canvas.height = 200;
            
            // Create gradient
            const design = designs[currentDesign];
            const gradient = ctx.createLinearGradient(0, 0, 200, 200);
            
            if (design.bg.includes('#2563eb')) {
                gradient.addColorStop(0, '#2563eb');
                gradient.addColorStop(1, '#3b82f6');
            } else if (design.bg.includes('#059669')) {
                gradient.addColorStop(0, '#059669');
                gradient.addColorStop(1, '#10b981');
            } else if (design.bg.includes('#dc2626')) {
                gradient.addColorStop(0, '#dc2626');
                gradient.addColorStop(1, '#ef4444');
            } else if (design.bg.includes('#7c3aed')) {
                gradient.addColorStop(0, '#7c3aed');
                gradient.addColorStop(1, '#a855f7');
            } else if (design.bg.includes('#ea580c')) {
                gradient.addColorStop(0, '#ea580c');
                gradient.addColorStop(1, '#f97316');
            } else {
                gradient.addColorStop(0, '#0891b2');
                gradient.addColorStop(1, '#06b6d4');
            }
            
            // Draw background with rounded corners
            ctx.fillStyle = gradient;
            roundRect(ctx, 0, 0, 200, 200, 20);
            ctx.fill();
            
            // Draw icon (simplified shield)
            ctx.fillStyle = 'white';
            ctx.font = 'bold 80px Arial';
            ctx.textAlign = 'center';
            ctx.textBaseline = 'middle';
            
            // Draw a simple shield shape
            ctx.beginPath();
            ctx.moveTo(100, 40);
            ctx.lineTo(140, 60);
            ctx.lineTo(140, 120);
            ctx.quadraticCurveTo(140, 160, 100, 160);
            ctx.quadraticCurveTo(60, 160, 60, 120);
            ctx.lineTo(60, 60);
            ctx.closePath();
            ctx.fill();
            
            // Add inner details
            ctx.fillStyle = design.bg.includes('#2563eb') ? '#2563eb' : '#1e40af';
            ctx.font = 'bold 40px Arial';
            ctx.fillText('S', 100, 100);
            
            // Download
            canvas.toBlob(function(blob) {
                const url = URL.createObjectURL(blob);
                const a = document.createElement('a');
                a.href = url;
                a.download = 'logo.jpg';
                document.body.appendChild(a);
                a.click();
                document.body.removeChild(a);
                URL.revokeObjectURL(url);
                
                alert('تم تحميل الشعار بنجاح! ضعه في مجلد النظام باسم "logo.jpg"');
            }, 'image/jpeg', 0.9);
        }
        
        function roundRect(ctx, x, y, width, height, radius) {
            ctx.beginPath();
            ctx.moveTo(x + radius, y);
            ctx.lineTo(x + width - radius, y);
            ctx.quadraticCurveTo(x + width, y, x + width, y + radius);
            ctx.lineTo(x + width, y + height - radius);
            ctx.quadraticCurveTo(x + width, y + height, x + width - radius, y + height);
            ctx.lineTo(x + radius, y + height);
            ctx.quadraticCurveTo(x, y + height, x, y + height - radius);
            ctx.lineTo(x, y + radius);
            ctx.quadraticCurveTo(x, y, x + radius, y);
            ctx.closePath();
        }
    </script>
</body>
</html>
