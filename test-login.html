<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>اختبار تسجيل الدخول</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            padding: 20px;
            background: #f5f5f5;
        }
        .test-container {
            max-width: 600px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .test-section {
            margin-bottom: 20px;
            padding: 15px;
            border: 1px solid #ddd;
            border-radius: 5px;
        }
        button {
            background: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover {
            background: #0056b3;
        }
        .result {
            margin-top: 10px;
            padding: 10px;
            border-radius: 5px;
        }
        .success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .error {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        .info {
            background: #d1ecf1;
            color: #0c5460;
            border: 1px solid #bee5eb;
        }
    </style>
</head>
<body>
    <div class="test-container">
        <h1>اختبار نظام تسجيل الدخول</h1>
        
        <div class="test-section">
            <h3>1. فحص localStorage</h3>
            <button onclick="checkLocalStorage()">فحص localStorage</button>
            <div id="localStorageResult" class="result"></div>
        </div>

        <div class="test-section">
            <h3>2. إنشاء المستخدمين الافتراضيين</h3>
            <button onclick="createDefaultUsers()">إنشاء المستخدمين</button>
            <div id="createUsersResult" class="result"></div>
        </div>

        <div class="test-section">
            <h3>3. اختبار المصادقة</h3>
            <button onclick="testAuth('admin', 'admin123')">اختبار admin</button>
            <button onclick="testAuth('analyst', 'analyst123')">اختبار analyst</button>
            <button onclick="testAuth('operator', 'operator123')">اختبار operator</button>
            <div id="authResult" class="result"></div>
        </div>

        <div class="test-section">
            <h3>4. مسح البيانات</h3>
            <button onclick="clearData()">مسح جميع البيانات</button>
            <div id="clearResult" class="result"></div>
        </div>

        <div class="test-section">
            <h3>5. الانتقال لصفحة تسجيل الدخول</h3>
            <button onclick="goToLogin()">فتح صفحة تسجيل الدخول</button>
        </div>
    </div>

    <script>
        // Simple password hashing function (same as in auth.js)
        function hashPassword(password) {
            let hash = 0;
            for (let i = 0; i < password.length; i++) {
                const char = password.charCodeAt(i);
                hash = ((hash << 5) - hash) + char;
                hash = hash & hash;
            }
            return hash.toString();
        }

        function checkLocalStorage() {
            const result = document.getElementById('localStorageResult');
            try {
                const users = JSON.parse(localStorage.getItem('systemUsers')) || {};
                const sessions = JSON.parse(localStorage.getItem('userSessions')) || {};
                const currentSession = localStorage.getItem('currentSession');
                
                result.className = 'result info';
                result.innerHTML = `
                    <strong>المستخدمين المحفوظين:</strong> ${Object.keys(users).length}<br>
                    <strong>الجلسات النشطة:</strong> ${Object.keys(sessions).length}<br>
                    <strong>الجلسة الحالية:</strong> ${currentSession || 'لا توجد'}<br>
                    <strong>المستخدمين:</strong> ${Object.keys(users).join(', ') || 'لا يوجد'}
                `;
            } catch (error) {
                result.className = 'result error';
                result.innerHTML = `خطأ: ${error.message}`;
            }
        }

        function createDefaultUsers() {
            const result = document.getElementById('createUsersResult');
            try {
                const users = {
                    'admin': {
                        id: 'admin',
                        username: 'admin',
                        password: hashPassword('admin123'),
                        fullName: 'مدير النظام',
                        email: '<EMAIL>',
                        role: 'admin',
                        permissions: ['read', 'write', 'delete', 'manage_users', 'view_analytics', 'manage_system'],
                        isActive: true,
                        createdAt: new Date().toISOString(),
                        lastLogin: null
                    },
                    'analyst': {
                        id: 'analyst',
                        username: 'analyst',
                        password: hashPassword('analyst123'),
                        fullName: 'محلل أمني',
                        email: '<EMAIL>',
                        role: 'analyst',
                        permissions: ['read', 'write', 'view_analytics'],
                        isActive: true,
                        createdAt: new Date().toISOString(),
                        lastLogin: null
                    },
                    'operator': {
                        id: 'operator',
                        username: 'operator',
                        password: hashPassword('operator123'),
                        fullName: 'مشغل النظام',
                        email: '<EMAIL>',
                        role: 'operator',
                        permissions: ['read'],
                        isActive: true,
                        createdAt: new Date().toISOString(),
                        lastLogin: null
                    }
                };

                localStorage.setItem('systemUsers', JSON.stringify(users));
                
                result.className = 'result success';
                result.innerHTML = 'تم إنشاء المستخدمين الافتراضيين بنجاح!';
            } catch (error) {
                result.className = 'result error';
                result.innerHTML = `خطأ: ${error.message}`;
            }
        }

        function testAuth(username, password) {
            const result = document.getElementById('authResult');
            try {
                const users = JSON.parse(localStorage.getItem('systemUsers')) || {};
                const user = users[username];
                
                if (!user) {
                    result.className = 'result error';
                    result.innerHTML = `المستخدم ${username} غير موجود`;
                    return;
                }

                const hashedPassword = hashPassword(password);
                if (user.password === hashedPassword) {
                    result.className = 'result success';
                    result.innerHTML = `نجح تسجيل الدخول للمستخدم ${username} (${user.fullName})`;
                } else {
                    result.className = 'result error';
                    result.innerHTML = `فشل تسجيل الدخول للمستخدم ${username} - كلمة المرور خاطئة`;
                }
            } catch (error) {
                result.className = 'result error';
                result.innerHTML = `خطأ: ${error.message}`;
            }
        }

        function clearData() {
            const result = document.getElementById('clearResult');
            try {
                localStorage.removeItem('systemUsers');
                localStorage.removeItem('userSessions');
                localStorage.removeItem('currentSession');
                localStorage.removeItem('securityEvents');
                localStorage.removeItem('currentEventId');
                localStorage.removeItem('activityLog');
                
                result.className = 'result success';
                result.innerHTML = 'تم مسح جميع البيانات بنجاح!';
            } catch (error) {
                result.className = 'result error';
                result.innerHTML = `خطأ: ${error.message}`;
            }
        }

        function goToLogin() {
            window.location.href = 'login.html';
        }

        // تشغيل فحص localStorage عند تحميل الصفحة
        window.onload = function() {
            checkLocalStorage();
        };
    </script>
</body>
</html>
