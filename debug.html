<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>تشخيص المشاكل</title>
    <style>
        body { font-family: Arial, sans-serif; padding: 20px; background: #f5f5f5; }
        .container { max-width: 800px; margin: 0 auto; background: white; padding: 20px; border-radius: 10px; }
        .section { margin-bottom: 20px; padding: 15px; border: 1px solid #ddd; border-radius: 5px; }
        button { background: #007bff; color: white; border: none; padding: 10px 20px; border-radius: 5px; cursor: pointer; margin: 5px; }
        .result { margin-top: 10px; padding: 10px; border-radius: 5px; white-space: pre-wrap; }
        .success { background: #d4edda; color: #155724; }
        .error { background: #f8d7da; color: #721c24; }
        .info { background: #d1ecf1; color: #0c5460; }
        pre { background: #f8f9fa; padding: 10px; border-radius: 5px; overflow-x: auto; }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔍 تشخيص مشاكل تسجيل الدخول</h1>
        
        <div class="section">
            <h3>1. فحص البيئة</h3>
            <button onclick="checkEnvironment()">فحص البيئة</button>
            <div id="envResult" class="result"></div>
        </div>

        <div class="section">
            <h3>2. فحص localStorage</h3>
            <button onclick="checkStorage()">فحص التخزين</button>
            <button onclick="clearStorage()">مسح التخزين</button>
            <div id="storageResult" class="result"></div>
        </div>

        <div class="section">
            <h3>3. إنشاء المستخدمين</h3>
            <button onclick="createUsers()">إنشاء المستخدمين</button>
            <div id="usersResult" class="result"></div>
        </div>

        <div class="section">
            <h3>4. اختبار المصادقة</h3>
            <button onclick="testLogin()">اختبار تسجيل الدخول</button>
            <div id="loginResult" class="result"></div>
        </div>

        <div class="section">
            <h3>5. اختبار الجلسات</h3>
            <button onclick="testSession()">اختبار الجلسة</button>
            <div id="sessionResult" class="result"></div>
        </div>

        <div class="section">
            <h3>6. الانتقال للصفحات</h3>
            <button onclick="goToSimpleLogin()">صفحة تسجيل الدخول البسيطة</button>
            <button onclick="goToOriginalLogin()">صفحة تسجيل الدخول الأصلية</button>
            <button onclick="goToMainApp()">التطبيق الرئيسي</button>
        </div>
    </div>

    <script>
        function hashPassword(password) {
            let hash = 0;
            for (let i = 0; i < password.length; i++) {
                const char = password.charCodeAt(i);
                hash = ((hash << 5) - hash) + char;
                hash = hash & hash;
            }
            return hash.toString();
        }

        function showResult(elementId, message, type = 'info') {
            const element = document.getElementById(elementId);
            element.className = `result ${type}`;
            element.textContent = message;
        }

        function checkEnvironment() {
            const info = {
                userAgent: navigator.userAgent,
                localStorage: typeof(Storage) !== "undefined",
                currentURL: window.location.href,
                protocol: window.location.protocol,
                host: window.location.host,
                pathname: window.location.pathname
            };
            
            showResult('envResult', JSON.stringify(info, null, 2), 'info');
        }

        function checkStorage() {
            try {
                const data = {
                    systemUsers: localStorage.getItem('systemUsers'),
                    userSessions: localStorage.getItem('userSessions'),
                    currentSession: localStorage.getItem('currentSession'),
                    securityEvents: localStorage.getItem('securityEvents'),
                    currentEventId: localStorage.getItem('currentEventId')
                };
                
                let result = 'محتويات localStorage:\n\n';
                for (const [key, value] of Object.entries(data)) {
                    result += `${key}: ${value ? 'موجود' : 'غير موجود'}\n`;
                    if (value && key === 'systemUsers') {
                        try {
                            const users = JSON.parse(value);
                            result += `  المستخدمين: ${Object.keys(users).join(', ')}\n`;
                        } catch (e) {
                            result += `  خطأ في تحليل البيانات: ${e.message}\n`;
                        }
                    }
                }
                
                showResult('storageResult', result, 'info');
            } catch (error) {
                showResult('storageResult', `خطأ: ${error.message}`, 'error');
            }
        }

        function clearStorage() {
            try {
                localStorage.clear();
                showResult('storageResult', 'تم مسح جميع البيانات من localStorage', 'success');
            } catch (error) {
                showResult('storageResult', `خطأ في مسح البيانات: ${error.message}`, 'error');
            }
        }

        function createUsers() {
            try {
                const users = {
                    'admin': {
                        id: 'admin',
                        username: 'admin',
                        password: hashPassword('admin123'),
                        fullName: 'مدير النظام',
                        email: '<EMAIL>',
                        role: 'admin',
                        permissions: ['read', 'write', 'delete', 'manage_users', 'view_analytics', 'manage_system'],
                        isActive: true,
                        createdAt: new Date().toISOString(),
                        lastLogin: null
                    },
                    'analyst': {
                        id: 'analyst',
                        username: 'analyst',
                        password: hashPassword('analyst123'),
                        fullName: 'محلل أمني',
                        email: '<EMAIL>',
                        role: 'analyst',
                        permissions: ['read', 'write', 'view_analytics'],
                        isActive: true,
                        createdAt: new Date().toISOString(),
                        lastLogin: null
                    },
                    'operator': {
                        id: 'operator',
                        username: 'operator',
                        password: hashPassword('operator123'),
                        fullName: 'مشغل النظام',
                        email: '<EMAIL>',
                        role: 'operator',
                        permissions: ['read'],
                        isActive: true,
                        createdAt: new Date().toISOString(),
                        lastLogin: null
                    }
                };

                localStorage.setItem('systemUsers', JSON.stringify(users));
                
                let result = 'تم إنشاء المستخدمين:\n\n';
                for (const [username, user] of Object.entries(users)) {
                    result += `${username}: ${user.fullName} (${user.role})\n`;
                    result += `  كلمة المرور المشفرة: ${user.password}\n`;
                    result += `  الصلاحيات: ${user.permissions.join(', ')}\n\n`;
                }
                
                showResult('usersResult', result, 'success');
            } catch (error) {
                showResult('usersResult', `خطأ: ${error.message}`, 'error');
            }
        }

        function testLogin() {
            try {
                const users = JSON.parse(localStorage.getItem('systemUsers')) || {};
                
                if (Object.keys(users).length === 0) {
                    showResult('loginResult', 'لا توجد مستخدمين. قم بإنشاء المستخدمين أولاً.', 'error');
                    return;
                }
                
                let result = 'اختبار تسجيل الدخول:\n\n';
                
                const testCases = [
                    { username: 'admin', password: 'admin123' },
                    { username: 'analyst', password: 'analyst123' },
                    { username: 'operator', password: 'operator123' },
                    { username: 'admin', password: 'wrong' },
                    { username: 'nonexistent', password: 'test' }
                ];
                
                testCases.forEach(test => {
                    const user = users[test.username];
                    if (!user) {
                        result += `❌ ${test.username}: المستخدم غير موجود\n`;
                        return;
                    }
                    
                    const hashedPassword = hashPassword(test.password);
                    if (user.password === hashedPassword) {
                        result += `✅ ${test.username}: نجح تسجيل الدخول\n`;
                    } else {
                        result += `❌ ${test.username}: فشل تسجيل الدخول (كلمة مرور خاطئة)\n`;
                    }
                });
                
                showResult('loginResult', result, 'info');
            } catch (error) {
                showResult('loginResult', `خطأ: ${error.message}`, 'error');
            }
        }

        function testSession() {
            try {
                // Create a test session
                const sessionId = 'test_session_' + Date.now();
                const session = {
                    userId: 'admin',
                    username: 'admin',
                    role: 'admin',
                    permissions: ['read', 'write', 'delete', 'manage_users', 'view_analytics', 'manage_system'],
                    createdAt: new Date().toISOString(),
                    expiresAt: new Date(Date.now() + 8 * 60 * 60 * 1000).toISOString()
                };
                
                const sessions = JSON.parse(localStorage.getItem('userSessions')) || {};
                sessions[sessionId] = session;
                
                localStorage.setItem('userSessions', JSON.stringify(sessions));
                localStorage.setItem('currentSession', sessionId);
                
                let result = 'تم إنشاء جلسة اختبار:\n\n';
                result += `معرف الجلسة: ${sessionId}\n`;
                result += `المستخدم: ${session.username}\n`;
                result += `الدور: ${session.role}\n`;
                result += `تاريخ الإنشاء: ${session.createdAt}\n`;
                result += `تاريخ الانتهاء: ${session.expiresAt}\n`;
                
                showResult('sessionResult', result, 'success');
            } catch (error) {
                showResult('sessionResult', `خطأ: ${error.message}`, 'error');
            }
        }

        function goToSimpleLogin() {
            window.location.href = 'simple-login.html';
        }

        function goToOriginalLogin() {
            window.location.href = 'login.html';
        }

        function goToMainApp() {
            window.location.href = 'index.html';
        }

        // Auto-run environment check on load
        window.onload = function() {
            checkEnvironment();
            checkStorage();
        };
    </script>
</body>
</html>
