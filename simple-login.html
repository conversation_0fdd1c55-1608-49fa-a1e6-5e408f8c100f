<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>تسجيل الدخول البسيط</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            padding: 20px;
            background: linear-gradient(135deg, #2563eb, #3b82f6);
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            margin: 0;
        }
        .login-container {
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.3);
            width: 100%;
            max-width: 400px;
        }
        .form-group {
            margin-bottom: 15px;
        }
        label {
            display: block;
            margin-bottom: 5px;
            font-weight: bold;
        }
        input {
            width: 100%;
            padding: 10px;
            border: 1px solid #ddd;
            border-radius: 5px;
            font-size: 16px;
            box-sizing: border-box;
        }
        button {
            width: 100%;
            padding: 12px;
            background: #2563eb;
            color: white;
            border: none;
            border-radius: 5px;
            font-size: 16px;
            cursor: pointer;
            margin-bottom: 10px;
        }
        button:hover {
            background: #1e40af;
        }
        .demo-btn {
            background: #6b7280;
            padding: 8px;
            font-size: 14px;
        }
        .demo-btn:hover {
            background: #4b5563;
        }
        .error {
            background: #fee;
            color: #c00;
            padding: 10px;
            border-radius: 5px;
            margin-bottom: 15px;
            display: none;
        }
        .success {
            background: #efe;
            color: #060;
            padding: 10px;
            border-radius: 5px;
            margin-bottom: 15px;
            display: none;
        }
        h2 {
            text-align: center;
            margin-bottom: 20px;
            color: #333;
        }
    </style>
</head>
<body>
    <div class="login-container">
        <h2>🛡️ تسجيل الدخول</h2>
        
        <div id="errorMsg" class="error"></div>
        <div id="successMsg" class="success"></div>
        
        <form id="loginForm">
            <div class="form-group">
                <label for="username">اسم المستخدم:</label>
                <input type="text" id="username" required>
            </div>
            
            <div class="form-group">
                <label for="password">كلمة المرور:</label>
                <input type="password" id="password" required>
            </div>
            
            <button type="submit" id="loginBtn">تسجيل الدخول</button>
        </form>
        
        <hr style="margin: 20px 0;">
        
        <h3 style="text-align: center; margin-bottom: 10px;">حسابات تجريبية:</h3>
        <button class="demo-btn" onclick="fillLogin('admin', 'admin123')">مدير النظام</button>
        <button class="demo-btn" onclick="fillLogin('analyst', 'analyst123')">محلل أمني</button>
        <button class="demo-btn" onclick="fillLogin('operator', 'operator123')">مشغل</button>
    </div>

    <script>
        // Simple password hashing
        function hashPassword(password) {
            let hash = 0;
            for (let i = 0; i < password.length; i++) {
                const char = password.charCodeAt(i);
                hash = ((hash << 5) - hash) + char;
                hash = hash & hash;
            }
            return hash.toString();
        }

        // Initialize default users
        function initUsers() {
            const users = {
                'admin': {
                    id: 'admin',
                    username: 'admin',
                    password: hashPassword('admin123'),
                    fullName: 'مدير النظام',
                    role: 'admin',
                    permissions: ['read', 'write', 'delete', 'manage_users', 'view_analytics', 'manage_system'],
                    isActive: true
                },
                'analyst': {
                    id: 'analyst',
                    username: 'analyst',
                    password: hashPassword('analyst123'),
                    fullName: 'محلل أمني',
                    role: 'analyst',
                    permissions: ['read', 'write', 'view_analytics'],
                    isActive: true
                },
                'operator': {
                    id: 'operator',
                    username: 'operator',
                    password: hashPassword('operator123'),
                    fullName: 'مشغل النظام',
                    role: 'operator',
                    permissions: ['read'],
                    isActive: true
                }
            };
            
            localStorage.setItem('systemUsers', JSON.stringify(users));
            return users;
        }

        // Show message
        function showMessage(message, type) {
            const errorDiv = document.getElementById('errorMsg');
            const successDiv = document.getElementById('successMsg');
            
            errorDiv.style.display = 'none';
            successDiv.style.display = 'none';
            
            if (type === 'error') {
                errorDiv.textContent = message;
                errorDiv.style.display = 'block';
            } else {
                successDiv.textContent = message;
                successDiv.style.display = 'block';
            }
        }

        // Fill login form
        function fillLogin(username, password) {
            document.getElementById('username').value = username;
            document.getElementById('password').value = password;
        }

        // Handle login
        function handleLogin(e) {
            e.preventDefault();
            
            const username = document.getElementById('username').value.trim();
            const password = document.getElementById('password').value;
            
            if (!username || !password) {
                showMessage('يرجى إدخال اسم المستخدم وكلمة المرور', 'error');
                return;
            }
            
            // Get or create users
            let users = {};
            try {
                users = JSON.parse(localStorage.getItem('systemUsers')) || {};
            } catch (e) {
                users = {};
            }
            
            if (Object.keys(users).length === 0) {
                users = initUsers();
            }
            
            const user = users[username];
            if (!user) {
                showMessage('اسم المستخدم غير موجود', 'error');
                return;
            }
            
            const hashedPassword = hashPassword(password);
            if (user.password !== hashedPassword) {
                showMessage('كلمة المرور غير صحيحة', 'error');
                return;
            }
            
            // Create session
            const sessionId = 'session_' + Date.now() + '_' + Math.random().toString(36).substr(2, 9);
            const session = {
                userId: user.id,
                username: user.username,
                role: user.role,
                permissions: user.permissions,
                createdAt: new Date().toISOString(),
                expiresAt: new Date(Date.now() + 8 * 60 * 60 * 1000).toISOString()
            };
            
            const sessions = JSON.parse(localStorage.getItem('userSessions')) || {};
            sessions[sessionId] = session;
            
            localStorage.setItem('userSessions', JSON.stringify(sessions));
            localStorage.setItem('currentSession', sessionId);
            
            showMessage('تم تسجيل الدخول بنجاح! جاري التحويل...', 'success');
            
            setTimeout(() => {
                window.location.href = 'index.html';
            }, 1500);
        }

        // Setup event listeners
        document.getElementById('loginForm').addEventListener('submit', handleLogin);

        // Initialize users on page load
        window.onload = function() {
            let users = {};
            try {
                users = JSON.parse(localStorage.getItem('systemUsers')) || {};
            } catch (e) {
                users = {};
            }
            
            if (Object.keys(users).length === 0) {
                initUsers();
                showMessage('تم إنشاء الحسابات التجريبية', 'success');
            }
        };
    </script>
</body>
</html>
