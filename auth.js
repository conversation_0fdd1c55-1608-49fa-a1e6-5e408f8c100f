// Authentication and User Management System
class AuthManager {
    constructor() {
        this.currentUser = null;
        this.users = this.loadUsers();

        try {
            this.userSessions = JSON.parse(localStorage.getItem('userSessions')) || {};
            this.activityLog = JSON.parse(localStorage.getItem('activityLog')) || [];
        } catch (error) {
            console.error('Error loading data from localStorage:', error);
            this.userSessions = {};
            this.activityLog = [];
        }

        this.init();
    }

    init() {
        this.setupEventListeners();
        this.applyTheme();
        this.setupLogo();
        this.initializeDefaultUsers();
        this.checkExistingSession();
    }

    // Initialize default users if none exist
    initializeDefaultUsers() {
        if (Object.keys(this.users).length === 0) {
            this.users = {
                'admin': {
                    id: 'admin',
                    username: 'admin',
                    password: this.hashPassword('admin123'),
                    fullName: 'مدير النظام',
                    email: '<EMAIL>',
                    role: 'admin',
                    permissions: ['read', 'write', 'delete', 'manage_users', 'view_analytics', 'manage_system'],
                    isActive: true,
                    createdAt: new Date().toISOString(),
                    lastLogin: null
                },
                'analyst': {
                    id: 'analyst',
                    username: 'analyst',
                    password: this.hashPassword('analyst123'),
                    fullName: 'محلل أمني',
                    email: '<EMAIL>',
                    role: 'analyst',
                    permissions: ['read', 'write', 'view_analytics'],
                    isActive: true,
                    createdAt: new Date().toISOString(),
                    lastLogin: null
                },
                'operator': {
                    id: 'operator',
                    username: 'operator',
                    password: this.hashPassword('operator123'),
                    fullName: 'مشغل النظام',
                    email: '<EMAIL>',
                    role: 'operator',
                    permissions: ['read'],
                    isActive: true,
                    createdAt: new Date().toISOString(),
                    lastLogin: null
                }
            };
            this.saveUsers();
        }
    }

    // Setup logo with fallback
    setupLogo() {
        const logoImages = document.querySelectorAll('.logo-image');
        logoImages.forEach(img => {
            img.addEventListener('error', function() {
                // Hide the image and show the icon fallback
                this.style.display = 'none';
                const icon = this.nextElementSibling;
                if (icon && icon.tagName === 'I') {
                    icon.style.display = 'inline-block';
                }
            });

            img.addEventListener('load', function() {
                // Hide the icon fallback when image loads successfully
                const icon = this.nextElementSibling;
                if (icon && icon.tagName === 'I') {
                    icon.style.display = 'none';
                }
            });
        });
    }

    setupEventListeners() {
        // Login form
        const loginForm = document.getElementById('loginForm');
        if (loginForm) {
            loginForm.addEventListener('submit', (e) => {
                e.preventDefault();
                this.handleLogin();
            });
        }

        // Password toggle
        const passwordToggle = document.getElementById('passwordToggle');
        if (passwordToggle) {
            passwordToggle.addEventListener('click', () => {
                this.togglePasswordVisibility();
            });
        }

        // Theme toggle
        const themeToggle = document.getElementById('themeToggle');
        if (themeToggle) {
            themeToggle.addEventListener('click', () => {
                this.toggleTheme();
            });
        }

        // Keyboard shortcuts
        document.addEventListener('keydown', (e) => {
            // Enter key to submit form
            if (e.key === 'Enter' && !e.shiftKey && !e.ctrlKey && !e.altKey) {
                const loginForm = document.getElementById('loginForm');
                if (loginForm && document.activeElement &&
                    (document.activeElement.id === 'username' || document.activeElement.id === 'password')) {
                    e.preventDefault();
                    this.handleLogin();
                }
            }
        });

        // Clear errors when user starts typing
        const usernameField = document.getElementById('username');
        const passwordField = document.getElementById('password');

        if (usernameField) {
            usernameField.addEventListener('input', () => {
                this.clearError();
            });
        }

        if (passwordField) {
            passwordField.addEventListener('input', () => {
                this.clearError();
            });
        }

        // Logout functionality
        const logoutBtn = document.getElementById('logoutBtn');
        if (logoutBtn) {
            logoutBtn.addEventListener('click', () => {
                this.logout();
            });
        }
    }

    // Simple password hashing (in production, use proper hashing)
    hashPassword(password) {
        let hash = 0;
        for (let i = 0; i < password.length; i++) {
            const char = password.charCodeAt(i);
            hash = ((hash << 5) - hash) + char;
            hash = hash & hash; // Convert to 32-bit integer
        }
        return hash.toString();
    }

    // Handle login
    async handleLogin() {
        const username = document.getElementById('username').value.trim();
        const password = document.getElementById('password').value;
        const rememberMe = document.getElementById('rememberMe').checked;

        if (!username || !password) {
            this.showError('يرجى إدخال اسم المستخدم وكلمة المرور');
            return;
        }

        this.showLoading(true);

        // Authenticate immediately
        const user = this.authenticateUser(username, password);

        if (user) {
            this.loginSuccess(user, rememberMe);
        } else {
            this.showError('اسم المستخدم أو كلمة المرور غير صحيح');
            this.showLoading(false);
        }
    }

    // Authenticate user
    authenticateUser(username, password) {
        const user = this.users[username];

        if (!user) {
            return null;
        }

        if (!user.isActive) {
            this.showError('هذا الحساب معطل');
            return null;
        }

        const hashedPassword = this.hashPassword(password);

        if (user.password === hashedPassword) {
            return user;
        }

        return null;
    }

    // Login success
    loginSuccess(user, rememberMe) {
        try {
            this.currentUser = user;

            // Update last login
            user.lastLogin = new Date().toISOString();
            this.users[user.username] = user;
            this.saveUsers();

            // Create session
            const sessionId = this.generateSessionId();
            const session = {
                userId: user.id,
                username: user.username,
                role: user.role,
                permissions: user.permissions,
                createdAt: new Date().toISOString(),
                expiresAt: rememberMe ?
                    new Date(Date.now() + 30 * 24 * 60 * 60 * 1000).toISOString() : // 30 days
                    new Date(Date.now() + 8 * 60 * 60 * 1000).toISOString() // 8 hours
            };

            this.userSessions[sessionId] = session;
            localStorage.setItem('userSessions', JSON.stringify(this.userSessions));
            localStorage.setItem('currentSession', sessionId);

            // Log activity
            this.logActivity('login', `تسجيل دخول المستخدم ${user.fullName}`);

            // Redirect to main application
            window.location.href = 'index.html';
        } catch (error) {
            console.error('Error during login success:', error);
            this.showError('حدث خطأ أثناء تسجيل الدخول');
            this.showLoading(false);
        }
    }

    // Generate session ID
    generateSessionId() {
        return 'session_' + Date.now() + '_' + Math.random().toString(36).substr(2, 9);
    }

    // Check existing session
    checkExistingSession() {
        // Only redirect if we're on login page and have valid session
        if (!window.location.pathname.includes('login.html')) {
            return false;
        }

        const sessionId = localStorage.getItem('currentSession');
        if (!sessionId) return false;

        const session = this.userSessions[sessionId];
        if (!session) return false;

        // Check if session is expired
        if (new Date() > new Date(session.expiresAt)) {
            this.clearSession(sessionId);
            return false;
        }

        // Session is valid - redirect to main page
        this.currentUser = this.users[session.username];
        if (this.currentUser) {
            setTimeout(() => {
                window.location.href = 'index.html';
            }, 500);
            return true;
        }

        return false;
    }

    // Clear session
    clearSession(sessionId) {
        delete this.userSessions[sessionId];
        localStorage.setItem('userSessions', JSON.stringify(this.userSessions));
        localStorage.removeItem('currentSession');
    }

    // Logout
    logout() {
        if (this.currentUser) {
            this.logActivity('logout', `تسجيل خروج المستخدم ${this.currentUser.fullName}`);
        }

        const sessionId = localStorage.getItem('currentSession');
        if (sessionId) {
            this.clearSession(sessionId);
        }

        this.currentUser = null;
        window.location.href = 'login.html';
    }

    // Check if user has permission
    hasPermission(permission) {
        if (!this.currentUser) return false;
        return this.currentUser.permissions.includes(permission);
    }

    // Get current user
    getCurrentUser() {
        return this.currentUser;
    }

    // Show loading state
    showLoading(show) {
        const loginBtn = document.getElementById('loginBtn');
        if (!loginBtn) return;

        const btnText = loginBtn.querySelector('.btn-text');
        const btnLoading = loginBtn.querySelector('.btn-loading');
        const btnArrow = loginBtn.querySelector('.btn-arrow');

        if (show) {
            loginBtn.disabled = true;
            if (btnText) btnText.style.opacity = '0';
            if (btnLoading) btnLoading.style.display = 'block';
            if (btnArrow) btnArrow.style.opacity = '0';
        } else {
            loginBtn.disabled = false;
            if (btnText) btnText.style.opacity = '1';
            if (btnLoading) btnLoading.style.display = 'none';
            if (btnArrow) btnArrow.style.opacity = '1';
        }
    }

    // Show error message
    showError(message) {
        const errorDiv = document.getElementById('loginError');
        const errorMessage = document.getElementById('errorMessage');

        if (!errorDiv || !errorMessage) {
            console.error('Error elements not found');
            alert(message); // Fallback to alert
            return;
        }

        errorMessage.textContent = message;
        errorDiv.style.display = 'flex';

        setTimeout(() => {
            errorDiv.style.display = 'none';
        }, 5000);
    }

    // Clear error message
    clearError() {
        const errorDiv = document.getElementById('loginError');
        if (errorDiv) {
            errorDiv.style.display = 'none';
        }
    }

    // Toggle password visibility
    togglePasswordVisibility() {
        const passwordInput = document.getElementById('password');
        const toggleIcon = document.querySelector('#passwordToggle i');
        
        if (passwordInput.type === 'password') {
            passwordInput.type = 'text';
            toggleIcon.className = 'fas fa-eye-slash';
        } else {
            passwordInput.type = 'password';
            toggleIcon.className = 'fas fa-eye';
        }
    }

    // Theme management
    toggleTheme() {
        const currentTheme = localStorage.getItem('theme') || 'light';
        const newTheme = currentTheme === 'light' ? 'dark' : 'light';
        this.applyTheme(newTheme);
        localStorage.setItem('theme', newTheme);
    }

    applyTheme(theme = null) {
        const currentTheme = theme || localStorage.getItem('theme') || 'light';
        document.documentElement.setAttribute('data-theme', currentTheme);
        
        const themeIcon = document.querySelector('#themeToggle i');
        if (themeIcon) {
            themeIcon.className = currentTheme === 'light' ? 'fas fa-moon' : 'fas fa-sun';
        }
    }

    // Activity logging
    logActivity(action, description) {
        const activity = {
            id: Date.now(),
            userId: this.currentUser ? this.currentUser.id : 'anonymous',
            username: this.currentUser ? this.currentUser.username : 'anonymous',
            action: action,
            description: description,
            timestamp: new Date().toISOString(),
            ip: 'localhost', // In production, get real IP
            userAgent: navigator.userAgent
        };

        this.activityLog.unshift(activity);
        
        // Keep only last 1000 activities
        if (this.activityLog.length > 1000) {
            this.activityLog = this.activityLog.slice(0, 1000);
        }

        localStorage.setItem('activityLog', JSON.stringify(this.activityLog));
    }

    // Load users from storage
    loadUsers() {
        try {
            return JSON.parse(localStorage.getItem('systemUsers')) || {};
        } catch (error) {
            console.error('Error loading users from localStorage:', error);
            return {};
        }
    }

    // Save users to storage
    saveUsers() {
        try {
            localStorage.setItem('systemUsers', JSON.stringify(this.users));
        } catch (error) {
            console.error('Error saving users to localStorage:', error);
            this.showError('خطأ في حفظ بيانات المستخدمين');
        }
    }
}



// Initialize auth manager
const authManager = new AuthManager();

// Export for use in other files
if (typeof module !== 'undefined' && module.exports) {
    module.exports = AuthManager;
}
