<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>تحديث كلمة مرور المدير</title>
    <style>
        body {
            font-family: 'Arial', sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            margin: 0;
            padding: 20px;
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
        }
        .container {
            background: white;
            padding: 30px;
            border-radius: 15px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.2);
            text-align: center;
            max-width: 500px;
            width: 100%;
        }
        h1 {
            color: #333;
            margin-bottom: 20px;
        }
        .info {
            background: #e3f2fd;
            padding: 15px;
            border-radius: 8px;
            margin: 20px 0;
            border-left: 4px solid #2196f3;
        }
        .success {
            background: #e8f5e8;
            border-left-color: #4caf50;
            color: #2e7d32;
        }
        button {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border: none;
            padding: 12px 30px;
            border-radius: 25px;
            cursor: pointer;
            font-size: 16px;
            margin: 10px;
            transition: all 0.3s ease;
        }
        button:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(0,0,0,0.2);
        }
        .redirect-btn {
            background: linear-gradient(135deg, #4caf50 0%, #45a049 100%);
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔐 تحديث كلمة مرور المدير</h1>
        
        <div class="info">
            <p><strong>كلمة المرور الجديدة للمدير:</strong></p>
            <p style="font-size: 18px; font-weight: bold; color: #1976d2;">NABnab@$2025</p>
        </div>

        <div id="status" class="info">
            <p>اضغط على الزر أدناه لتحديث كلمة مرور المدير وإعادة تعيين النظام</p>
        </div>

        <button onclick="updateAdminPassword()" id="updateBtn">
            تحديث كلمة المرور
        </button>

        <button onclick="goToLogin()" id="loginBtn" class="redirect-btn" style="display: none;">
            الذهاب إلى صفحة تسجيل الدخول
        </button>
    </div>

    <script>
        function updateAdminPassword() {
            try {
                // Clear all existing data
                localStorage.removeItem('systemUsers');
                localStorage.removeItem('userSessions');
                localStorage.removeItem('currentSession');
                localStorage.removeItem('securityEvents');
                localStorage.removeItem('eventTypes');
                localStorage.removeItem('postIncidentReviews');
                localStorage.removeItem('activityLog');
                
                // Show success message
                const statusDiv = document.getElementById('status');
                statusDiv.className = 'info success';
                statusDiv.innerHTML = `
                    <p><strong>✅ تم التحديث بنجاح!</strong></p>
                    <p>تم تحديث كلمة مرور المدير إلى: <strong>NABnab@$2025</strong></p>
                    <p>تم إعادة تعيين جميع البيانات المحفوظة</p>
                `;
                
                // Hide update button and show login button
                document.getElementById('updateBtn').style.display = 'none';
                document.getElementById('loginBtn').style.display = 'inline-block';
                
            } catch (error) {
                console.error('Error updating password:', error);
                const statusDiv = document.getElementById('status');
                statusDiv.innerHTML = `
                    <p><strong>❌ حدث خطأ!</strong></p>
                    <p>فشل في تحديث كلمة المرور: ${error.message}</p>
                `;
            }
        }

        function goToLogin() {
            window.location.href = 'login.html';
        }
    </script>
</body>
</html>
